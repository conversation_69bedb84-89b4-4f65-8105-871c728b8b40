# نظام إدارة المنتجات المتكامل

## نظرة عامة

تم تطوير نظام إدارة المنتجات ليتعامل مع قاعدة بيانات MySQL بدلاً من ملفات JSON، مع دعم كامل لإدارة:
- البيانات الأساسية للمنتجات
- صور المنتجات
- مميزات المنتجات
- مواصفات المنتجات

## هيكل قاعدة البيانات

### الجداول المستخدمة

1. **products** - البيانات الأساسية للمنتج
2. **product_images** - صور المنتج
3. **product_features** - مميزات المنتج
4. **product_specifications** - مواصفات المنتج

### العلاقات

- كل منتج يمكن أن يحتوي على عدة صور (One-to-Many)
- كل منتج يمكن أن يحتوي على عدة مميزات (One-to-Many)
- كل منتج يمكن أن يحتوي على عدة مواصفات (One-to-Many)

## الملفات المحدثة

### 1. src/lib/mysql-database.ts
- إضافة دوال متكاملة لإدارة المنتجات:
  - `addProductWithDetails()` - إضافة منتج مع جميع التفاصيل
  - `updateProductWithDetails()` - تحديث منتج مع التفاصيل
  - `deleteProductWithDetails()` - حذف منتج مع جميع البيانات المرتبطة
  - `getProductWithDetails()` - جلب منتج مع جميع التفاصيل
  - `getProductsWithDetails()` - جلب جميع المنتجات مع التفاصيل

### 2. src/pages/api/admin/products.ts
- تحديث API endpoints لدعم البيانات المتكاملة
- دعم عمليات CRUD الكاملة
- معالجة أفضل للأخطاء مع رسائل باللغة العربية

### 3. src/pages/admin/products.tsx
- تحديث واجهة المستخدم لدعم النظام الجديد
- عرض الصور والمميزات والمواصفات
- نماذج محسنة لإدخال البيانات

## كيفية الاستخدام

### إضافة منتج جديد

```javascript
const productData = {
  product: {
    id: 'unique-id',
    title: 'Product Name',
    title_ar: 'اسم المنتج',
    description: 'Product description',
    description_ar: 'وصف المنتج',
    price: 100.00,
    original_price: 150.00,
    is_available: true,
    category_id: 'category-id',
    subcategory_id: 'subcategory-id',
    is_active: true,
    is_featured: false
  },
  images: ['url1.jpg', 'url2.jpg'],
  features: [
    { text: 'Feature 1', textAr: 'الميزة 1' }
  ],
  specifications: [
    { key: 'Material', keyAr: 'المادة', value: 'Steel', valueAr: 'فولاذ' }
  ]
};

const newProduct = await addProductWithDetails(productData);
```

### تحديث منتج

```javascript
const updateData = {
  product: { price: 120.00 },
  images: ['new-url1.jpg', 'new-url2.jpg'],
  features: [
    { text: 'Updated Feature', textAr: 'ميزة محدثة' }
  ]
};

const updatedProduct = await updateProductWithDetails(productId, updateData);
```

### جلب منتج مع التفاصيل

```javascript
const product = await getProductWithDetails(productId);
console.log(product.images); // صور المنتج
console.log(product.features); // مميزات المنتج
console.log(product.specifications); // مواصفات المنتج
```

## API Endpoints

### GET /api/admin/products
- جلب جميع المنتجات مع التفاصيل
- دعم التصفية حسب الفئة والفئة الفرعية

### GET /api/admin/products?id={productId}
- جلب منتج محدد مع جميع التفاصيل

### POST /api/admin/products
- إضافة منتج جديد مع الصور والمميزات والمواصفات

### PUT /api/admin/products?id={productId}
- تحديث منتج موجود

### DELETE /api/admin/products?id={productId}
- حذف منتج مع جميع البيانات المرتبطة

## هيكل البيانات

### ProductWithDetails Interface

```typescript
interface ProductWithDetails extends Product {
  images: ProductImage[];
  features: ProductFeature[];
  specifications: ProductSpecification[];
}
```

### ProductImage Interface

```typescript
interface ProductImage {
  id: number;
  product_id: string;
  image_url: string;
  sort_order: number;
  created_at: Date;
}
```

### ProductFeature Interface

```typescript
interface ProductFeature {
  id: number;
  product_id: string;
  feature_text: string;
  feature_text_ar: string;
  sort_order: number;
}
```

### ProductSpecification Interface

```typescript
interface ProductSpecification {
  id: number;
  product_id: string;
  spec_key: string;
  spec_key_ar: string;
  spec_value: string;
  spec_value_ar: string;
  sort_order: number;
}
```

## الاختبار

تم إنشاء ملف اختبار في `src/test/products-test.js` لاختبار جميع العمليات:

```bash
node src/test/products-test.js
```

## المميزات الجديدة

1. **إدارة متكاملة**: جميع البيانات المرتبطة بالمنتج تُدار في عملية واحدة
2. **أمان البيانات**: استخدام transactions لضمان تماسك البيانات
3. **أداء محسن**: استعلامات محسنة لجلب البيانات
4. **واجهة محسنة**: عرض أفضل للبيانات في لوحة التحكم
5. **معالجة الأخطاء**: معالجة شاملة للأخطاء مع رسائل واضحة

## ملاحظات مهمة

- تأكد من وجود اتصال صحيح بقاعدة البيانات MySQL
- جميع العمليات تستخدم soft delete للمنتجات
- الصور والمميزات والمواصفات تُحذف نهائياً عند حذف المنتج
- يتم ترتيب البيانات حسب sort_order

## الدعم

للمساعدة أو الإبلاغ عن مشاكل، يرجى مراجعة الكود أو التواصل مع فريق التطوير.
