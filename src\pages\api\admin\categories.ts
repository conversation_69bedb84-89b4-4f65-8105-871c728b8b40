import { NextApiRequest, NextApiResponse } from 'next';
import { getCategories, addCategory, updateCategory, deleteCategory, getCategoryById } from '../../../lib/mysql-database';
import { requireAdminAuth } from '../../../lib/auth';
import { v4 as uuidv4 } from 'uuid';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // التحقق من المصادقة والصلاحيات
  const user = requireAdminAuth(req);
  if (!user) {
    return res.status(401).json({
      success: false,
      message: 'Authentication required',
      messageAr: 'المصادقة مطلوبة'
    });
  }

  try {
    switch (req.method) {
      case 'GET':
        const categories = await getCategories();
        res.status(200).json({ success: true, data: categories });
        break;

      case 'POST':
        const { name, nameAr, description, descriptionAr, image, isActive } = req.body;

        if (!name || !nameAr) {
          return res.status(400).json({
            success: false,
            message: 'Name and Arabic name are required',
            messageAr: 'الاسم والاسم بالعربية مطلوبان'
          });
        }

        const categoryData = {
          id: uuidv4(),
          name,
          name_ar: nameAr,
          description: description || null,
          description_ar: descriptionAr || null,
          image: image || null,
          is_active: isActive !== undefined ? isActive : true
        };

        const newCategory = await addCategory(categoryData);
        res.status(201).json({ success: true, data: newCategory });
        break;

      case 'PUT':
        const { id } = req.query;
        
        if (!id || typeof id !== 'string') {
          return res.status(400).json({ 
            success: false, 
            message: 'Category ID is required' 
          });
        }

        const existingCategory = getCategoryById(id);
        if (!existingCategory) {
          return res.status(404).json({ 
            success: false, 
            message: 'Category not found' 
          });
        }

        const updatedCategory = updateCategory(id, req.body);
        res.status(200).json({ success: true, data: updatedCategory });
        break;

      case 'DELETE':
        const { id: deleteId } = req.query;
        
        if (!deleteId || typeof deleteId !== 'string') {
          return res.status(400).json({ 
            success: false, 
            message: 'Category ID is required' 
          });
        }

        const deleted = deleteCategory(deleteId);
        
        if (!deleted) {
          return res.status(404).json({ 
            success: false, 
            message: 'Category not found' 
          });
        }

        res.status(200).json({ 
          success: true, 
          message: 'Category deleted successfully' 
        });
        break;

      default:
        res.setHeader('Allow', ['GET', 'POST', 'PUT', 'DELETE']);
        res.status(405).json({ 
          success: false, 
          message: `Method ${req.method} not allowed` 
        });
    }
  } catch (error) {
    console.error('Categories API error:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Internal server error' 
    });
  }
}
