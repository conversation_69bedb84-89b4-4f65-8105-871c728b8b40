import { NextApiRequest, NextApiResponse } from 'next';
import {
  getProductsWithDetails,
  addProductWithDetails,
  updateProductWithDetails,
  deleteProductWithDetails,
  getProductWithDetails,
  getProductsByCategory,
  getProductsBySubcategory
} from '../../../lib/mysql-database';
import { requireAdminAuth } from '../../../lib/auth';
import { v4 as uuidv4 } from 'uuid';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // التحقق من المصادقة والصلاحيات
  const user = requireAdminAuth(req);
  if (!user) {
    return res.status(401).json({
      success: false,
      message: 'Authentication required',
      messageAr: 'المصادقة مطلوبة'
    });
  }

  try {
    switch (req.method) {
      case 'GET':
        const { categoryId, subcategoryId, id } = req.query;

        // إذا تم تمرير ID محدد، جلب منتج واحد مع التفاصيل
        if (id && typeof id === 'string') {
          const product = await getProductWithDetails(id);
          if (!product) {
            return res.status(404).json({
              success: false,
              message: 'Product not found',
              messageAr: 'المنتج غير موجود'
            });
          }
          return res.status(200).json({ success: true, data: product });
        }

        // جلب المنتجات حسب الفئة أو الفئة الفرعية أو جميع المنتجات
        let products;
        if (subcategoryId && typeof subcategoryId === 'string') {
          products = await getProductsBySubcategory(subcategoryId);
        } else if (categoryId && typeof categoryId === 'string') {
          products = await getProductsByCategory(categoryId);
        } else {
          products = await getProductsWithDetails();
        }

        res.status(200).json({ success: true, data: products });
        break;

      case 'POST':
        const {
          title,
          titleAr,
          description,
          descriptionAr,
          images,
          price,
          originalPrice,
          available,
          categoryId: catId,
          subcategoryId: subCatId,
          features,
          featuresAr,
          specifications,
          isActive,
          isFeatured
        } = req.body;

        if (!title || !titleAr || !description || !descriptionAr || !catId || !subCatId || price === undefined) {
          return res.status(400).json({
            success: false,
            message: 'Required fields are missing',
            messageAr: 'الحقول المطلوبة مفقودة'
          });
        }

        // تحضير بيانات المنتج
        const productId = uuidv4();
        const productData = {
          product: {
            id: productId,
            title,
            title_ar: titleAr,
            description,
            description_ar: descriptionAr,
            price: parseFloat(price),
            original_price: originalPrice ? parseFloat(originalPrice) : null,
            is_available: available !== undefined ? available : true,
            category_id: catId,
            subcategory_id: subCatId,
            is_active: isActive !== undefined ? isActive : true,
            is_featured: isFeatured !== undefined ? isFeatured : false
          },
          images: images && Array.isArray(images) ? images.filter(img => img && img.trim()) : [],
          features: [],
          specifications: []
        };

        // تحضير المميزات
        if (features && featuresAr && Array.isArray(features) && Array.isArray(featuresAr)) {
          for (let i = 0; i < Math.min(features.length, featuresAr.length); i++) {
            if (features[i] && featuresAr[i] && features[i].trim() && featuresAr[i].trim()) {
              productData.features.push({
                text: features[i].trim(),
                textAr: featuresAr[i].trim()
              });
            }
          }
        }

        // تحضير المواصفات
        if (specifications && Array.isArray(specifications)) {
          for (const spec of specifications) {
            if (spec.nameEn && spec.nameAr && spec.valueEn && spec.valueAr &&
                spec.nameEn.trim() && spec.nameAr.trim() && spec.valueEn.trim() && spec.valueAr.trim()) {
              productData.specifications.push({
                key: spec.nameEn.trim(),
                keyAr: spec.nameAr.trim(),
                value: spec.valueEn.trim(),
                valueAr: spec.valueAr.trim()
              });
            }
          }
        }

        const newProduct = await addProductWithDetails(productData);
        res.status(201).json({ success: true, data: newProduct });
        break;

      case 'PUT':
        const { id } = req.query;

        if (!id || typeof id !== 'string') {
          return res.status(400).json({
            success: false,
            message: 'Product ID is required',
            messageAr: 'معرف المنتج مطلوب'
          });
        }

        const existingProduct = await getProductWithDetails(id);
        if (!existingProduct) {
          return res.status(404).json({
            success: false,
            message: 'Product not found',
            messageAr: 'المنتج غير موجود'
          });
        }

        const {
          title,
          titleAr,
          description,
          descriptionAr,
          images,
          price,
          originalPrice,
          available,
          categoryId: catId,
          subcategoryId: subCatId,
          features,
          featuresAr,
          specifications,
          isActive,
          isFeatured
        } = req.body;

        // تحضير بيانات التحديث
        const updateData: any = {};

        // تحديث بيانات المنتج الأساسية
        if (title !== undefined || titleAr !== undefined || description !== undefined ||
            descriptionAr !== undefined || price !== undefined || originalPrice !== undefined ||
            available !== undefined || catId !== undefined || subCatId !== undefined ||
            isActive !== undefined || isFeatured !== undefined) {

          updateData.product = {};

          if (title !== undefined) updateData.product.title = title;
          if (titleAr !== undefined) updateData.product.title_ar = titleAr;
          if (description !== undefined) updateData.product.description = description;
          if (descriptionAr !== undefined) updateData.product.description_ar = descriptionAr;
          if (price !== undefined) updateData.product.price = parseFloat(price);
          if (originalPrice !== undefined) updateData.product.original_price = originalPrice ? parseFloat(originalPrice) : null;
          if (available !== undefined) updateData.product.is_available = available;
          if (catId !== undefined) updateData.product.category_id = catId;
          if (subCatId !== undefined) updateData.product.subcategory_id = subCatId;
          if (isActive !== undefined) updateData.product.is_active = isActive;
          if (isFeatured !== undefined) updateData.product.is_featured = isFeatured;
        }

        // تحديث الصور
        if (images !== undefined && Array.isArray(images)) {
          updateData.images = images.filter(img => img && img.trim());
        }

        // تحديث المميزات
        if (features !== undefined && featuresAr !== undefined &&
            Array.isArray(features) && Array.isArray(featuresAr)) {
          updateData.features = [];
          for (let i = 0; i < Math.min(features.length, featuresAr.length); i++) {
            if (features[i] && featuresAr[i] && features[i].trim() && featuresAr[i].trim()) {
              updateData.features.push({
                text: features[i].trim(),
                textAr: featuresAr[i].trim()
              });
            }
          }
        }

        // تحديث المواصفات
        if (specifications !== undefined && Array.isArray(specifications)) {
          updateData.specifications = [];
          for (const spec of specifications) {
            if (spec.nameEn && spec.nameAr && spec.valueEn && spec.valueAr &&
                spec.nameEn.trim() && spec.nameAr.trim() && spec.valueEn.trim() && spec.valueAr.trim()) {
              updateData.specifications.push({
                key: spec.nameEn.trim(),
                keyAr: spec.nameAr.trim(),
                value: spec.valueEn.trim(),
                valueAr: spec.valueAr.trim()
              });
            }
          }
        }

        const updatedProduct = await updateProductWithDetails(id, updateData);
        res.status(200).json({ success: true, data: updatedProduct });
        break;

      case 'DELETE':
        const { id: deleteId } = req.query;

        if (!deleteId || typeof deleteId !== 'string') {
          return res.status(400).json({
            success: false,
            message: 'Product ID is required',
            messageAr: 'معرف المنتج مطلوب'
          });
        }

        const deleted = await deleteProductWithDetails(deleteId);

        if (!deleted) {
          return res.status(404).json({
            success: false,
            message: 'Product not found',
            messageAr: 'المنتج غير موجود'
          });
        }

        res.status(200).json({
          success: true,
          message: 'Product deleted successfully',
          messageAr: 'تم حذف المنتج بنجاح'
        });
        break;

      default:
        res.setHeader('Allow', ['GET', 'POST', 'PUT', 'DELETE']);
        res.status(405).json({
          success: false,
          message: `Method ${req.method} not allowed`,
          messageAr: `الطريقة ${req.method} غير مسموحة`
        });
    }
  } catch (error) {
    console.error('Products API error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      messageAr: 'خطأ داخلي في الخادم',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
}
