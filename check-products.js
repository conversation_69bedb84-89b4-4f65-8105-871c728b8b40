const mysql = require('mysql2/promise');

async function checkProducts() {
  try {
    const connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: '',
      database: 'droobhajer_db'
    });
    
    const [rows] = await connection.execute('SELECT id, title_ar, price FROM products WHERE deleted_at IS NULL');
    console.log('Available products in database:');
    console.log('Total products found:', rows.length);
    rows.forEach(product => {
      console.log(`- ID: ${product.id}, Title: ${product.title_ar}, Price: ${product.price}`);
    });

    // Check if sample data exists
    const [sampleRows] = await connection.execute("SELECT id, title_ar FROM products WHERE id IN ('prod-1', 'prod-2', 'prod-3', 'prod-4')");
    console.log('\nSample products (prod-1, prod-2, etc.):');
    console.log('Sample products found:', sampleRows.length);
    sampleRows.forEach(product => {
      console.log(`- ID: ${product.id}, Title: ${product.title_ar}`);
    });
    
    await connection.end();
  } catch (error) {
    console.error('Error:', error.message);
  }
}

checkProducts();
