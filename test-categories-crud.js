// اختبار عمليات CRUD للفئات والفئات الفرعية
const baseUrl = 'http://localhost:3000';

// بيانات اختبار للفئة الرئيسية
const testCategory = {
  name: 'Test Category',
  nameAr: 'فئة تجريبية',
  description: 'Test category description',
  descriptionAr: 'وصف فئة تجريبية',
  image: '',
  isActive: true
};

// بيانات اختبار للفئة الفرعية
const testSubcategory = {
  name: 'Test Subcategory',
  nameAr: 'فئة فرعية تجريبية',
  categoryId: '', // سيتم تعيينه بعد إنشاء الفئة الرئيسية
  description: 'Test subcategory description',
  descriptionAr: 'وصف فئة فرعية تجريبية',
  isActive: true
};

let createdCategoryId = null;
let createdSubcategoryId = null;

// اختبار إضافة فئة رئيسية
async function testAddCategory() {
  console.log('🧪 اختبار إضافة فئة رئيسية...');
  
  try {
    const response = await fetch(`${baseUrl}/api/admin/categories`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testCategory),
    });

    if (response.ok) {
      const result = await response.json();
      createdCategoryId = result.data.id;
      console.log('✅ تم إضافة الفئة الرئيسية بنجاح:', result.data.name_ar);
      return true;
    } else {
      console.error('❌ فشل في إضافة الفئة الرئيسية:', response.status);
      return false;
    }
  } catch (error) {
    console.error('❌ خطأ في اختبار إضافة الفئة الرئيسية:', error.message);
    return false;
  }
}

// اختبار جلب الفئات الرئيسية
async function testGetCategories() {
  console.log('🧪 اختبار جلب الفئات الرئيسية...');
  
  try {
    const response = await fetch(`${baseUrl}/api/admin/categories`);
    
    if (response.ok) {
      const result = await response.json();
      console.log('✅ تم جلب الفئات الرئيسية بنجاح');
      console.log('📊 عدد الفئات:', result.data.length);
      return true;
    } else {
      console.error('❌ فشل في جلب الفئات الرئيسية:', response.status);
      return false;
    }
  } catch (error) {
    console.error('❌ خطأ في اختبار جلب الفئات الرئيسية:', error.message);
    return false;
  }
}

// اختبار تحديث فئة رئيسية
async function testUpdateCategory() {
  if (!createdCategoryId) {
    console.log('❌ لا يوجد معرف فئة للتحديث');
    return false;
  }

  console.log('🧪 اختبار تحديث فئة رئيسية...');
  
  try {
    const updateData = {
      nameAr: 'فئة تجريبية محدثة',
      description: 'Updated test category description',
      isActive: false
    };

    const response = await fetch(`${baseUrl}/api/admin/categories?id=${createdCategoryId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(updateData),
    });

    if (response.ok) {
      console.log('✅ تم تحديث الفئة الرئيسية بنجاح');
      return true;
    } else {
      console.error('❌ فشل في تحديث الفئة الرئيسية:', response.status);
      return false;
    }
  } catch (error) {
    console.error('❌ خطأ في اختبار تحديث الفئة الرئيسية:', error.message);
    return false;
  }
}

// اختبار إضافة فئة فرعية
async function testAddSubcategory() {
  if (!createdCategoryId) {
    console.log('❌ لا يوجد معرف فئة رئيسية لإضافة فئة فرعية');
    return false;
  }

  console.log('🧪 اختبار إضافة فئة فرعية...');
  
  try {
    testSubcategory.categoryId = createdCategoryId;
    
    const response = await fetch(`${baseUrl}/api/admin/subcategories`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testSubcategory),
    });

    if (response.ok) {
      const result = await response.json();
      createdSubcategoryId = result.data.id;
      console.log('✅ تم إضافة الفئة الفرعية بنجاح:', result.data.name_ar);
      return true;
    } else {
      console.error('❌ فشل في إضافة الفئة الفرعية:', response.status);
      return false;
    }
  } catch (error) {
    console.error('❌ خطأ في اختبار إضافة الفئة الفرعية:', error.message);
    return false;
  }
}

// اختبار جلب الفئات الفرعية
async function testGetSubcategories() {
  console.log('🧪 اختبار جلب الفئات الفرعية...');
  
  try {
    const response = await fetch(`${baseUrl}/api/admin/subcategories`);
    
    if (response.ok) {
      const result = await response.json();
      console.log('✅ تم جلب الفئات الفرعية بنجاح');
      console.log('📊 عدد الفئات الفرعية:', result.data.length);
      return true;
    } else {
      console.error('❌ فشل في جلب الفئات الفرعية:', response.status);
      return false;
    }
  } catch (error) {
    console.error('❌ خطأ في اختبار جلب الفئات الفرعية:', error.message);
    return false;
  }
}

// اختبار حذف فئة فرعية
async function testDeleteSubcategory() {
  if (!createdSubcategoryId) {
    console.log('❌ لا يوجد معرف فئة فرعية للحذف');
    return false;
  }

  console.log('🧪 اختبار حذف فئة فرعية...');
  
  try {
    const response = await fetch(`${baseUrl}/api/admin/subcategories?id=${createdSubcategoryId}`, {
      method: 'DELETE',
    });

    if (response.ok) {
      console.log('✅ تم حذف الفئة الفرعية بنجاح');
      return true;
    } else {
      console.error('❌ فشل في حذف الفئة الفرعية:', response.status);
      return false;
    }
  } catch (error) {
    console.error('❌ خطأ في اختبار حذف الفئة الفرعية:', error.message);
    return false;
  }
}

// اختبار حذف فئة رئيسية
async function testDeleteCategory() {
  if (!createdCategoryId) {
    console.log('❌ لا يوجد معرف فئة للحذف');
    return false;
  }

  console.log('🧪 اختبار حذف فئة رئيسية...');
  
  try {
    const response = await fetch(`${baseUrl}/api/admin/categories?id=${createdCategoryId}`, {
      method: 'DELETE',
    });

    if (response.ok) {
      console.log('✅ تم حذف الفئة الرئيسية بنجاح');
      return true;
    } else {
      console.error('❌ فشل في حذف الفئة الرئيسية:', response.status);
      return false;
    }
  } catch (error) {
    console.error('❌ خطأ في اختبار حذف الفئة الرئيسية:', error.message);
    return false;
  }
}

// تشغيل جميع الاختبارات
async function runAllTests() {
  console.log('🚀 بدء اختبار عمليات CRUD للفئات والفئات الفرعية\n');
  
  // اختبار الفئات الرئيسية
  console.log('📁 اختبار الفئات الرئيسية:');
  await testAddCategory();
  await testGetCategories();
  await testUpdateCategory();
  
  // اختبار الفئات الفرعية
  console.log('\n📂 اختبار الفئات الفرعية:');
  await testAddSubcategory();
  await testGetSubcategories();
  
  // اختبار الحذف
  console.log('\n🗑️ اختبار الحذف:');
  await testDeleteSubcategory();
  await testDeleteCategory();
  
  console.log('\n🏁 انتهاء الاختبارات');
}

// تشغيل الاختبارات
runAllTests();
