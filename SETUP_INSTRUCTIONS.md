# تعليمات إعداد نظام إدارة المنتجات

## 🚀 خطوات التشغيل السريع

### 1. تأكد من تشغيل MySQL
```bash
# تأكد من تشغيل خادم MySQL
# في Windows: تشغيل XAMPP أو WAMP
# في Linux/Mac: 
sudo systemctl start mysql
```

### 2. إنشاء قاعدة البيانات
```sql
-- في MySQL Command Line أو phpMyAdmin
CREATE DATABASE droobhajer_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE droobhajer_db;

-- تشغيل ملف إنشاء الجداول
SOURCE droobhajer_db.sql;

-- تشغيل البيانات التجريبية
SOURCE sample_data.sql;
```

### 3. إعداد متغيرات البيئة
إنشاء ملف `.env.local` في جذر المشروع:
```env
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=your_mysql_password
DB_NAME=droobhajer_db
```

### 4. اختبار الاتصال
```bash
node test-db-connection.js
```

### 5. تشغيل المشروع
```bash
npm run dev
```

### 6. اختبار النظام
- اذهب إلى: `http://localhost:3000/admin/products`
- يجب أن تظهر المنتجات التجريبية

## 🔧 حل المشاكل الشائعة

### مشكلة: خطأ اتصال قاعدة البيانات
```
Error: connect ECONNREFUSED 127.0.0.1:3306
```
**الحل:**
1. تأكد من تشغيل MySQL
2. تحقق من بيانات الاتصال في `.env.local`

### مشكلة: قاعدة البيانات غير موجودة
```
Error: Unknown database 'droobhajer_db'
```
**الحل:**
```sql
CREATE DATABASE droobhajer_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

### مشكلة: الجداول غير موجودة
```
Error: Table 'droobhajer_db.products' doesn't exist
```
**الحل:**
```sql
USE droobhajer_db;
SOURCE droobhajer_db.sql;
```

### مشكلة: لا توجد بيانات
**الحل:**
```sql
USE droobhajer_db;
SOURCE sample_data.sql;
```

## 📊 التحقق من البيانات

### عرض الفئات:
```sql
SELECT * FROM categories;
```

### عرض المنتجات:
```sql
SELECT * FROM products;
```

### عرض منتج مع تفاصيله:
```sql
SELECT 
    p.*,
    GROUP_CONCAT(DISTINCT pi.image_url) as images,
    GROUP_CONCAT(DISTINCT pf.feature_text_ar) as features,
    GROUP_CONCAT(DISTINCT ps.spec_key_ar, ': ', ps.spec_value_ar) as specifications
FROM products p
LEFT JOIN product_images pi ON p.id = pi.product_id
LEFT JOIN product_features pf ON p.id = pf.product_id  
LEFT JOIN product_specifications ps ON p.id = ps.product_id
WHERE p.id = 'prod-1'
GROUP BY p.id;
```

## 🎯 نقاط الاختبار

1. **صفحة المنتجات**: `/admin/products`
   - عرض قائمة المنتجات
   - البحث والتصفية
   - إضافة منتج جديد
   - تعديل منتج موجود
   - حذف منتج

2. **API Endpoints**:
   - `GET /api/admin/products` - جلب المنتجات
   - `POST /api/admin/products` - إضافة منتج
   - `PUT /api/admin/products?id=xxx` - تحديث منتج
   - `DELETE /api/admin/products?id=xxx` - حذف منتج

3. **اختبار البيانات المتكاملة**:
   - التأكد من ظهور الصور
   - التأكد من ظهور المميزات
   - التأكد من ظهور المواصفات

## 📝 ملاحظات مهمة

- تم إزالة المصادقة مؤقتاً للاختبار
- جميع العمليات تستخدم MySQL بدلاً من JSON
- البيانات التجريبية تحتوي على 4 منتجات كاملة
- النظام يدعم اللغتين العربية والإنجليزية

## 🔄 إعادة تفعيل المصادقة

عند الانتهاء من الاختبار، قم بإعادة تفعيل المصادقة في:
- `src/pages/api/admin/products.ts`
- `src/pages/api/admin/categories.ts`
- `src/pages/api/admin/subcategories.ts`

بإزالة التعليقات من كود `requireAdminAuth`.
